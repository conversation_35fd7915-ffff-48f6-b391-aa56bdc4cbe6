export function parsePrice(value: string | number | undefined | null): number {
	if (typeof value === "number") return isNaN(value) ? 0 : value;
	if (!value || value === "" || value === "0") return 0;
	const cleanValue = value
		.toString()
		.replace(/R\$\s?/g, "")
		.replace(/\./g, "")
		.replace(",", ".")
		.trim();

	const numericValue = parseFloat(cleanValue);
	return isNaN(numericValue) ? 0 : numericValue;
}

export function formatPrice(value: number | string | undefined | null): string {
	const numericValue = typeof value === "string" ? parsePrice(value) : (value ?? 0);

	if (isNaN(numericValue)) return "R$ 0,00";

	return new Intl.NumberFormat("pt-BR", {
		style: "currency",
		currency: "BRL",
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	}).format(numericValue);
}

export function formatPriceForInput(value: number | undefined | null): number | undefined {
	if (!value || isNaN(value) || value === 0) return undefined;
	return value;
}

export function formatPriceForDisplay(value: number | undefined | null): number | undefined {
	const numericValue = parsePrice(value);
	return numericValue > 0 ? numericValue : undefined;
}

export function isValidPrice(value: string | number | undefined | null): boolean {
	const numericValue = parsePrice(value);
	return numericValue > 0;
}

export function calculateMargin(costPrice: number, salePrice: number): number {
	if (!costPrice || !salePrice || salePrice <= 0) return 0;
	const profit = salePrice - costPrice;
	return (profit / salePrice) * 100;
}

export function calculateSalePrice(costPrice: number, marginPercentage: number): number {
	if (!costPrice || marginPercentage === undefined || marginPercentage === 100) return 0;
	return costPrice / (1 - marginPercentage / 100);
}

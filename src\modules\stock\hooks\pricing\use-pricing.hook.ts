import { useState, useCallback, useMemo } from "react";
import { UseFormReturn } from "react-hook-form";
import { parsePrice, formatPriceForDisplay } from "@/shared/utils/price-formatter";
import { ICreateStock } from "../../validators/create-stock.validator";

interface IUsePricingHook {
	index: number;
	methodsForm: UseFormReturn<ICreateStock>;
}

interface IPricingData {
	costPrice: number;
	salePrice: number;
	displayCostPrice: number | undefined;
	displaySalePrice: number | undefined;
	showCalculator: boolean;
	profit: number;
	margin: number;
	roi: number;
}

interface IPricingActions {
	setCostPrice: (value: number) => void;
	setSalePrice: (value: number) => void;
	toggleCalculator: () => void;
	closeCalculator: () => void;
	calculatePriceFromMargin: (margin: number) => void;
}

export const usePricing = ({ index, methodsForm }: IUsePricingHook): IPricingData & IPricingActions => {
	const [showCalculator, setShowCalculator] = useState(false);
	const { watch, setValue } = methodsForm;

	// Watch values with minimal re-renders
	const costPriceRaw = watch(`inventories.${index}.stockMovement.product.costPrice`);
	const salePriceRaw = watch(`inventories.${index}.stockMovement.product.price`);

	// Memoized parsed values
	const costPrice = useMemo(() => parsePrice(costPriceRaw), [costPriceRaw]);
	const salePrice = useMemo(() => parsePrice(salePriceRaw), [salePriceRaw]);

	// Memoized display values
	const displayCostPrice = useMemo(() => formatPriceForDisplay(costPrice), [costPrice]);
	const displaySalePrice = useMemo(() => formatPriceForDisplay(salePrice), [salePrice]);

	// Memoized calculations
	const calculations = useMemo(() => {
		if (!costPrice || !salePrice) {
			return { profit: 0, margin: 0, roi: 0 };
		}

		const profit = salePrice - costPrice;
		const margin = salePrice > 0 ? (profit / salePrice) * 100 : 0;
		const roi = costPrice > 0 ? (profit / costPrice) * 100 : 0;

		return {
			profit: Number(profit.toFixed(2)),
			margin: Number(margin.toFixed(2)),
			roi: Number(roi.toFixed(2)),
		};
	}, [costPrice, salePrice]);

	// Actions
	const setCostPrice = useCallback((value: number) => {
		setValue(`inventories.${index}.stockMovement.product.costPrice`, value, {
			shouldValidate: true,
			shouldDirty: true,
		});
	}, [setValue, index]);

	const setSalePrice = useCallback((value: number) => {
		setValue(`inventories.${index}.stockMovement.product.price`, value, {
			shouldValidate: true,
			shouldDirty: true,
		});
	}, [setValue, index]);

	const toggleCalculator = useCallback(() => {
		setShowCalculator(prev => !prev);
	}, []);

	const closeCalculator = useCallback(() => {
		setShowCalculator(false);
	}, []);

	const calculatePriceFromMargin = useCallback((margin: number) => {
		if (!costPrice || margin === undefined || margin === 100) return;
		
		const calculatedPrice = costPrice / (1 - margin / 100);
		const finalPrice = Number(calculatedPrice.toFixed(2));
		
		setSalePrice(finalPrice);
	}, [costPrice, setSalePrice]);

	return {
		// Data
		costPrice,
		salePrice,
		displayCostPrice,
		displaySalePrice,
		showCalculator,
		profit: calculations.profit,
		margin: calculations.margin,
		roi: calculations.roi,
		
		// Actions
		setCostPrice,
		setSalePrice,
		toggleCalculator,
		closeCalculator,
		calculatePriceFromMargin,
	};
};
